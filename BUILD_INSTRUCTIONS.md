# IR-Alchemist Windows Build Instructions

## Prerequisites

### 1. Python Installation
- Python 3.8 or higher
- Ensure Python is added to PATH during installation

### 2. Required Dependencies
Install all dependencies using pip:
```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install PyQt5>=5.15.0
pip install soundfile>=0.12.0
pip install numpy>=1.21.0
pip install scipy>=1.7.0
pip install pyinstaller>=5.0.0
```

## Build Process

### Method 1: Automated Build (Recommended)
1. Open Command Prompt or PowerShell in the project directory
2. Run the build script:
   ```bash
   build.bat
   ```
3. Wait for compilation to complete (may take 5-10 minutes)
4. Find the compiled application in `dist\IR-Alchemist\`

### Method 2: Manual Build
1. Open Command Prompt in the project directory
2. Clean previous builds (optional):
   ```bash
   rmdir /s /q dist
   rmdir /s /q build
   ```
3. Run PyInstaller:
   ```bash
   pyinstaller IR-Alchemist.spec
   ```
4. Find the compiled application in `dist\IR-Alchemist\`

## Required Files for Build

Ensure these files are present in the project directory:

### Core Application Files
- `IR-Alchemist.py` - Main application
- `ir_dataset.py` - Dataset manager
- `ir_bundle.py` - Bundled IR data (2.7MB)

### Assets
- `SpaceGrotesk-Regular.ttf` - Regular font
- `SpaceGrotesk-Bold.ttf` - Bold font
- `Sample.wav` - Demo audio file
- `IR-Alchemist.ico` - Application icon

### Build Configuration
- `IR-Alchemist.spec` - PyInstaller specification
- `build.bat` - Automated build script
- `requirements.txt` - Dependencies list

## Build Output

After successful compilation, you'll find:

```
dist/
└── IR-Alchemist/
    ├── IR-Alchemist.exe          (Main executable)
    ├── ir_dataset.py             (Dataset manager)
    ├── ir_bundle.py              (Bundled IR data)
    ├── SpaceGrotesk-Regular.ttf  (Font files)
    ├── SpaceGrotesk-Bold.ttf
    ├── Sample.wav                (Demo audio)
    ├── IR-Alchemist.ico          (Icon)
    └── [Various DLL and library files]
```

## Distribution

### Single Folder Distribution
- Distribute the entire `dist\IR-Alchemist` folder
- Users run `IR-Alchemist.exe` directly
- No installation required
- All dependencies included

### Creating an Installer (Optional)
You can use tools like:
- **Inno Setup** (Free) - Creates professional Windows installers
- **NSIS** (Free) - Nullsoft Scriptable Install System
- **Advanced Installer** (Commercial) - Feature-rich installer creator

## Troubleshooting

### Common Build Issues

**1. "PyInstaller not found"**
```bash
pip install pyinstaller
```

**2. "Missing module" errors**
- Check that all dependencies are installed
- Verify `requirements.txt` packages are installed

**3. "File not found" errors**
- Ensure all required files are in the project directory
- Check file names match exactly (case-sensitive)

**4. Large executable size**
- This is normal for PyQt5 applications
- Expected size: 150-200MB for the complete folder
- The bundled IR data adds ~3MB

**5. Antivirus false positives**
- Some antivirus software may flag PyInstaller executables
- This is a known issue with compiled Python applications
- Add exclusion for the build folder if needed

### Build Verification

Test the compiled application:
1. Navigate to `dist\IR-Alchemist\`
2. Double-click `IR-Alchemist.exe`
3. Verify:
   - Application window opens (1000x800)
   - SpaceGrotesk fonts load correctly
   - IR generation works for all styles
   - Audio preview functions
   - Export functionality works

## Performance Notes

- **Build time**: 5-10 minutes depending on system
- **Final size**: ~150-200MB (includes all dependencies)
- **Startup time**: 2-5 seconds (normal for PyQt5 applications)
- **Memory usage**: ~50-100MB during operation

## Advanced Options

### Single File Executable
To create a single .exe file instead of a folder:
1. Modify `IR-Alchemist.spec`:
   ```python
   exe = EXE(
       pyz,
       a.scripts,
       a.binaries,  # Add this line
       a.datas,     # Add this line
       [],
       name='IR-Alchemist',
       # ... other options
       onefile=True,  # Add this line
   )
   ```
2. Remove the `COLLECT` section
3. Rebuild with `pyinstaller IR-Alchemist.spec`

**Note**: Single file mode is slower to start and may have compatibility issues.

## Support

For build issues:
1. Check Python version (3.8+)
2. Verify all dependencies installed
3. Ensure all required files present
4. Check Windows compatibility (Windows 10/11 recommended)
