# IR-Alchemist Deployment Notes

## Final Deployment Steps

### 1. **Remove Original IR Folder** (Important for Protection)
Before distributing the application, remove the original "IR-Alchemist IRs" folder:
```
rmdir /s "IR-Alchemist IRs"
```
This ensures users cannot access the individual IR files directly.

### 2. **Required Files for Distribution**
The following files are needed for the application to work:
```
IR-Alchemist/
├── IR-Alchemist.py          (main application)
├── ir_dataset.py            (dataset manager)
├── ir_bundle.py             (protected IR data - 2.7MB)
├── Sample.wav               (demo audio file)
├── Exo-VariableFont_wght.ttf (application font)
└── CHANGES_SUMMARY.md       (optional documentation)
```

### 3. **Dependencies Required**
Users need these Python packages installed:
- PyQt5 (GUI framework)
- soundfile (audio file handling)
- numpy (numerical operations)
- scipy (signal processing)

### 4. **Application Features**
- **Protected Dataset**: 76 IR files bundled and compressed
- **4 Styles**: American, British, German, Random
- **Real-time Generation**: Instant IR creation from dataset
- **Preview System**: Audio preview with IR analysis
- **Export Options**: Individual, batch, and combined IR export
- **No ML Dependencies**: No PyTorch, CUDA, or model files needed

### 5. **Security Features**
- IR files are compressed with gzip
- Data is base64 encoded
- Stored as Python constants (not separate files)
- Cannot be easily extracted without reverse engineering
- Original WAV files are not accessible to users

### 6. **Performance Benefits**
- **Fast startup**: No model loading required
- **Instant generation**: Direct dataset processing
- **Low memory usage**: No GPU memory allocation
- **Cross-platform**: Works on any system with Python + PyQt5

### 7. **Usage Instructions**
1. Run `python IR-Alchemist.py`
2. Select desired IR style (American, British, German, Random)
3. Click "Generate IRs" to create 10 new IRs
4. Preview individual IRs or combined selections
5. Export IRs as WAV files (48kHz, 24-bit)

### 8. **Troubleshooting**
- If application doesn't start, check PyQt5 installation
- If no sound in preview, verify Sample.wav exists
- If IR generation fails, check ir_bundle.py integrity
- For font issues, verify Exo-VariableFont_wght.ttf is present

### 9. **Technical Notes**
- IR data is automatically resampled from 44.1kHz to 48kHz
- All IRs are normalized to 2048 samples
- Style processing applies frequency-domain shaping
- Diversity control adds random variations
- Export format is 48kHz, 24-bit WAV

### 10. **Distribution Checklist**
- [ ] Remove "IR-Alchemist IRs" folder
- [ ] Verify all required files are present
- [ ] Test application startup
- [ ] Test IR generation for all styles
- [ ] Test preview and export functions
- [ ] Confirm ir_bundle.py loads correctly
- [ ] Package files for distribution
