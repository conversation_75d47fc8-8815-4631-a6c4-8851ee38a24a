# IR-Alchemist Deployment Notes

## Final Deployment Steps

### 1. **Remove Original IR Folder** (Required for Distribution)
Before distributing the application, remove the original "IR-Alchemist IRs" folder:
```
rmdir /s "IR-Alchemist IRs"
```
**This is now required** - the application works entirely from the bundled data in `ir_bundle.py`.

### 2. **Required Files for Distribution**
The following files are needed for the application to work:
```
IR-Alchemist/
├── IR-Alchemist.py          (main application)
├── ir_dataset.py            (dataset manager)
├── ir_bundle.py             (protected IR data - 2.7MB)
├── Sample.wav               (demo audio file)
├── SpaceGrotesk-Regular.ttf (application font - regular)
├── SpaceGrotesk-Bold.ttf    (application font - bold)
└── CHANGES_SUMMARY.md       (optional documentation)
```

### 3. **Dependencies Required**
Users need these Python packages installed:
- PyQt5 (GUI framework)
- soundfile (audio file handling)
- numpy (numerical operations)
- scipy (signal processing)

### 4. **Application Features**
- **Self-Contained Dataset**: 76 IR files bundled in single 2.7MB file
- **4 Styles**: American, British, German, Random (19 IRs each)
- **Pure IR Generation**: Direct selection from dataset without post-processing
- **Unique Batches**: Each generation provides 10 different IRs (no duplicates)
- **Visual Preview**: Real-time frequency response and waveform graphs
- **Compact Interface**: 60% smaller visualization panel with graphical displays
- **Export Options**: Individual, batch, and combined IR export
- **No External Dependencies**: No PyTorch, matplotlib, or external IR files needed

### 5. **Security Features**
- IR files are compressed with gzip
- Data is base64 encoded
- Stored as Python constants (not separate files)
- Cannot be easily extracted without reverse engineering
- Original WAV files are not accessible to users

### 6. **Performance Benefits**
- **Fast startup**: No model loading required
- **Instant generation**: Direct dataset processing
- **Low memory usage**: No GPU memory allocation
- **Cross-platform**: Works on any system with Python + PyQt5

### 7. **Usage Instructions**
1. Run `python IR-Alchemist.py`
2. Application opens at 1000x800 pixels (minimum size)
3. Select desired IR style (American, British, German, Random)
4. Click "Generate IRs" to create 10 unique IRs
5. Preview individual IRs or combined selections
6. Export IRs as WAV files (48kHz, 24-bit)

### 8. **Troubleshooting**
- If application doesn't start, check PyQt5 installation
- If no sound in preview, verify Sample.wav exists
- If IR generation fails, check ir_bundle.py integrity
- For font issues, verify SpaceGrotesk-Regular.ttf and SpaceGrotesk-Bold.ttf are present

### 9. **Technical Notes**
- IR data is automatically resampled from 44.1kHz to 48kHz
- All IRs are normalized to 2048 samples
- **No post-processing applied** - IRs are used directly from dataset
- Unique selection ensures no duplicates within each batch of 10
- Export format is 48kHz, 24-bit WAV

### 10. **Windows Compilation**
To compile for Windows distribution:
```bash
# Install build dependencies
pip install -r requirements.txt

# Run automated build
build.bat

# Or manual build
pyinstaller IR-Alchemist.spec
```

### 11. **Distribution Checklist**
- [ ] Remove "IR-Alchemist IRs" folder (if present)
- [ ] Verify all required files are present
- [ ] Install dependencies: `pip install -r requirements.txt`
- [ ] Run build script: `build.bat`
- [ ] Test compiled executable in `dist\IR-Alchemist\`
- [ ] Verify application icon appears correctly
- [ ] Test IR generation for all styles
- [ ] Test preview and export functions
- [ ] Package `dist\IR-Alchemist` folder for distribution
