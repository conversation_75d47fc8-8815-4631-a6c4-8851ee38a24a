import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap, QIcon
import logging
from ir_dataset import IRDatasetManager

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# --- CarbonWidget: Custom widget with carbon background ---
class CarbonWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pattern = self.createCarbonPattern()

    def createCarbonPattern(self):
        # Create a 40x40 pixmap with a carbon-like pattern
        pixmap = QPixmap(40, 40)
        pixmap.fill(QColor("#2b2b2b"))  # Base dark color
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        whitePen = QPen(QColor(255, 255, 255, 15))  # ~6% opacity
        blackPen = QPen(QColor(0, 0, 0, 15))
        # Draw white lines
        painter.setPen(whitePen)
        painter.drawLine(0, 10, 30, 40)
        painter.drawLine(10, 0, 40, 30)
        # Draw black lines
        painter.setPen(blackPen)
        painter.drawLine(0, 20, 20, 40)
        painter.drawLine(20, 0, 40, 20)
        painter.end()
        return pixmap

    def paintEvent(self, event):
        painter = QPainter(self)
        brush = QBrush(self.pattern)
        painter.fillRect(self.rect(), brush)
        super().paintEvent(event)

# --- Custom Checkbox without SVG ---
class DarkCheckBox(QCheckBox):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #FFFFFF;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #555555;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #007ACC;
                background-color: #007ACC;
            }
        """)

# --- Custom Graph Widgets ---
class FrequencyGraphWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.freqs = None
        self.magnitude = None

    def update_data(self, freqs, magnitude):
        self.freqs = freqs
        self.magnitude = magnitude
        self.update()

    def paintEvent(self, event):
        painter = QPainter()
        if not painter.begin(self):
            return

        try:
            painter.setRenderHint(QPainter.Antialiasing)

            width = self.width()
            height = self.height()

            # Clear background
            painter.fillRect(0, 0, width, height, QColor("#000000"))

            if self.freqs is not None and self.magnitude is not None and len(self.magnitude) > 1:
                # Logarithmic frequency scale
                log_freqs = np.log10(self.freqs[1:] + 1)
                log_min = np.min(log_freqs)
                log_max = np.max(log_freqs)

                mag_data = self.magnitude[1:]
                mag_min = np.min(mag_data)
                mag_max = np.max(mag_data)

                if mag_max > mag_min and log_max > log_min:
                    painter.setPen(QPen(QColor("#007ACC"), 2))

                    prev_x = prev_y = None
                    step = max(1, len(log_freqs) // (width * 2))

                    for i in range(0, len(log_freqs), step):
                        x = int((log_freqs[i] - log_min) / (log_max - log_min) * (width - 1))
                        y = int((1.0 - (mag_data[i] - mag_min) / (mag_max - mag_min)) * (height - 1))

                        if prev_x is not None:
                            painter.drawLine(prev_x, prev_y, x, y)

                        prev_x, prev_y = x, y

            # Draw grid
            painter.setPen(QPen(QColor("#333333"), 1))
            for i in range(1, 4):
                y = int(i * height / 4)
                painter.drawLine(0, y, width, y)

        finally:
            painter.end()

class WaveformGraphWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.waveform = None

    def update_data(self, waveform):
        self.waveform = waveform
        self.update()

    def paintEvent(self, event):
        painter = QPainter()
        if not painter.begin(self):
            return

        try:
            painter.setRenderHint(QPainter.Antialiasing)

            width = self.width()
            height = self.height()

            # Clear background
            painter.fillRect(0, 0, width, height, QColor("#000000"))

            if self.waveform is not None and len(self.waveform) > 1:
                step = max(1, len(self.waveform) // width)
                downsampled = self.waveform[::step][:width]

                max_amp = np.max(np.abs(downsampled))
                if max_amp > 0:
                    normalized = downsampled / max_amp
                else:
                    normalized = downsampled

                painter.setPen(QPen(QColor("#00FF00"), 1))

                center_y = height // 2
                prev_x = prev_y = None

                for i, sample in enumerate(normalized):
                    x = int(i * (width - 1) / max(1, len(normalized) - 1))
                    y = int(center_y - sample * (center_y - 2))

                    if prev_x is not None:
                        painter.drawLine(prev_x, prev_y, x, y)

                    prev_x, prev_y = x, y

                # Draw center line
                painter.setPen(QPen(QColor("#444444"), 1))
                painter.drawLine(0, center_y, width, center_y)

        finally:
            painter.end()

# --- Compact Visual IR Display ---
class IRDetailDisplay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("background-color: #121212; color: #FFFFFF;")
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Create compact layout
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Compact title
        title_label = QLabel("IR Preview")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #007ACC; margin-bottom: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Create visual display area
        self.visual_widget = QWidget()
        self.visual_widget.setStyleSheet("background-color: #1a1a1a; border: 1px solid #444444; border-radius: 3px;")
        visual_layout = QVBoxLayout()
        visual_layout.setContentsMargins(8, 8, 8, 8)
        visual_layout.setSpacing(6)

        # Frequency response graph
        freq_label = QLabel("Frequency Response")
        freq_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #FFFFFF; margin-bottom: 2px;")
        visual_layout.addWidget(freq_label)

        self.freq_graph = FrequencyGraphWidget()
        self.freq_graph.setMinimumHeight(80)
        self.freq_graph.setMaximumHeight(80)
        self.freq_graph.setStyleSheet("background-color: #000000; border: 1px solid #333333;")
        visual_layout.addWidget(self.freq_graph)

        # Waveform display
        wave_label = QLabel("Waveform")
        wave_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #FFFFFF; margin: 4px 0 2px 0;")
        visual_layout.addWidget(wave_label)

        self.waveform_graph = WaveformGraphWidget()
        self.waveform_graph.setMinimumHeight(60)
        self.waveform_graph.setMaximumHeight(60)
        self.waveform_graph.setStyleSheet("background-color: #000000; border: 1px solid #333333;")
        visual_layout.addWidget(self.waveform_graph)

        # Compact info display
        self.info_label = QLabel("No IR selected")
        self.info_label.setStyleSheet("font-size: 11px; color: #AAAAAA; margin-top: 4px;")
        self.info_label.setAlignment(Qt.AlignCenter)
        visual_layout.addWidget(self.info_label)

        self.visual_widget.setLayout(visual_layout)
        layout.addWidget(self.visual_widget)

        layout.addStretch()
        self.setLayout(layout)

    def update_detail(self, ir, sample_rate=48000):
        """Update the display with compact visual IR analysis"""
        try:
            # Calculate frequency response
            n = len(ir)
            freqs = np.fft.rfftfreq(n, d=1/sample_rate)
            IR_fft = np.fft.rfft(ir)
            mag = np.abs(IR_fft)
            mag_db = 20 * np.log10(mag + 1e-9)

            # Update graphs with new data
            self.freq_graph.update_data(freqs, mag_db)
            self.waveform_graph.update_data(ir)

            # Update compact info
            duration = len(ir) / sample_rate
            max_amplitude = np.max(np.abs(ir))

            # Find dominant frequency
            peak_idx = np.argmax(mag_db[1:]) + 1  # Skip DC component
            dominant_freq = freqs[peak_idx]

            info_text = f"{duration:.2f}s • Peak: {max_amplitude:.3f} • Dom: {dominant_freq:.0f}Hz"
            self.info_label.setText(info_text)

        except Exception as e:
            self.info_label.setText(f"Analysis error: {str(e)}")



# --- IRProcessor ---
class IRProcessor:
    def __init__(self):
        self.dataset_manager = IRDatasetManager()  # Uses bundled data
        self.ir_length = 2048
        self.sample_rate = 48000

        logging.info(f"Initialized IR processor with {self.dataset_manager.get_ir_count()} bundled IR files")
        style_counts = self.dataset_manager.get_style_counts()
        for style, count in style_counts.items():
            logging.info(f"  {style}: {count} files")

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """Generate multiple IRs using the dataset-based approach"""
        try:
            irs = self.dataset_manager.generate_multiple_irs(
                diversity=diversity,
                count=count,
                style=style
            )
            logging.info(f"Generated {len(irs)} IRs with style '{style}' and diversity {diversity}")
            return irs
        except Exception as e:
            logging.error(f"Error generating IRs: {e}")
            # Return fallback IRs
            fallback_irs = []
            for i in range(count):
                fallback_ir = np.zeros(self.ir_length)
                fallback_ir[0] = 1.0  # Simple impulse
                fallback_irs.append(fallback_ir)
            return fallback_irs

# --- IR Generation Worker ---
class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, processor, diversity, style, count, seed):
        super().__init__()
        self.processor = processor
        self.diversity = diversity
        self.style = style
        self.count = count
        self.seed = seed

    @pyqtSlot()
    def run(self):
        try:
            if self.seed is not None:
                np.random.seed(self.seed)
            logging.info("Starting IR generation with diversity=%s, style=%s, count=%d", self.diversity, self.style, self.count)
            irs = self.processor.generate_multiple_irs(diversity=self.diversity, count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            logging.error("Error during IR generation: %s", str(e))
            self.error.emit(str(e))

# --- Main GUI Class ---
class IRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist")
        self.setGeometry(100, 100, 1000, 800)
        self.setMinimumSize(1000, 800)

        # Set application icon
        icon_path = os.path.join(os.path.dirname(__file__), "IR-Alchemist.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        self.last_generated_irs = None
        self.ir_widgets = []
        self.selected_style = "American"
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # --- Central widget with carbon background ---
        main_widget = CarbonWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # --- Header Section ---
        header_layout = QVBoxLayout()
        title_layout = QHBoxLayout()
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet("font-family: 'Space Grotesk'; font-size: 48px; font-weight: bold; color: #FFFFFF;")
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setOffset(5, 5)
        shadow.setColor(QColor(0, 0, 0, 220))
        title_label.setGraphicsEffect(shadow)
        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet("font-family: 'Space Grotesk'; font-size: 24px; font-style: italic; color: #AAAAAA; margin-left: 10px;")
        title_layout.addStretch()
        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)
        
        # Add subheading
        subheading_label = QLabel("Advanced Cabinet IR Generator for Guitarists Powered by AI")
        subheading_label.setStyleSheet("font-family: 'Space Grotesk'; font-size: 20px; color: #FFFFFF;")
        subheading_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subheading_label)
        
        header_layout.addSpacing(10)
        instructions = QLabel(
            "Select the IR sound characteristic below and click the 'Generate IRs' button to create 10 new cabinet IRs.\n"
            "Use the IR list on the left to select and preview individual IRs. The detail panel on the right displays the EQ curve and spectrogram of the selected IR."
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("font-family: 'Space Grotesk'; font-size: 16px; color: #EEEEEE;")
        instructions.setWordWrap(True)
        header_layout.addWidget(instructions)
        main_layout.addLayout(header_layout)

        # --- Combined IR Sound Characteristic selection and Generate Button ---
        # Create a horizontal layout with fixed height of 50 for both parts.
        style_group_box = QGroupBox("Select IR Sound Characteristic")
        style_group_box.setStyleSheet("""
            QGroupBox { 
                background-color: #444444; 
                font-size: 18px; 
                font-weight: bold; 
                color: #FFFFFF; 
                border: 2px solid #3F6AC2; 
                border-radius: 5px; 
                margin-top: 0.5em;
            } 
            QGroupBox::title { 
                subcontrol-origin: margin; 
                subcontrol-position: top center; 
                padding: 0 3px; 
                color: #FFFFFF; 
            }
        """)
        style_layout = QHBoxLayout()
        self.style_button_group = QButtonGroup()
        # Radio buttons in order: American, British, German, Random
        self.radio_american = QRadioButton("American")
        self.radio_british = QRadioButton("British")
        self.radio_german = QRadioButton("German")
        self.radio_custom = QRadioButton("Random")
        self.radio_american.setChecked(True)
        self.radio_american.setToolTip("Select the American cabinet IR style.")
        self.radio_british.setToolTip("Select the British cabinet IR style.")
        self.radio_german.setToolTip("Select the German cabinet IR style.")
        self.radio_custom.setToolTip("Select a random cabinet IR style.")
        for rb in (self.radio_american, self.radio_british, self.radio_german, self.radio_custom):
            rb.setStyleSheet("QRadioButton { font-size: 16px; color: #FFFFFF; } QRadioButton::indicator { width: 18px; height: 18px; } QRadioButton::indicator:unchecked { border: 1px solid #555555; background-color: #333333; border-radius: 9px; } QRadioButton::indicator:checked { border: 1px solid #007ACC; background-color: #007ACC; border-radius: 9px; }")
            self.style_button_group.addButton(rb)
        self.radio_american.toggled.connect(lambda: self.update_style("American"))
        self.radio_british.toggled.connect(lambda: self.update_style("British"))
        self.radio_german.toggled.connect(lambda: self.update_style("German"))
        self.radio_custom.toggled.connect(lambda: self.update_style("Random"))
        style_layout.addWidget(self.radio_american)
        style_layout.addWidget(self.radio_british)
        style_layout.addWidget(self.radio_german)
        style_layout.addWidget(self.radio_custom)
        style_group_box.setLayout(style_layout)
        style_group_box.setFixedHeight(50)

        self.generate_round_btn = QPushButton("Click here to generate 10 new cabinet IRs")
        self.generate_round_btn.setFixedHeight(50)
        self.generate_round_btn.setToolTip("Generate 10 new cabinet impulse responses using AI.")
        self.generate_round_btn.setStyleSheet("""
            QPushButton {
                background-color: #5481EA;
                border: 2px solid #3F6AC2;
                border-radius: 10px;
                font-size: 18px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #6B9DFA;
            }
            QPushButton:pressed {
                background-color: #4B78D0;
                border-style: inset;
            }
            QPushButton:disabled {
                background-color: #666666;
                border: 2px solid #555555;
                color: #AAAAAA;
            }
        """)
        self.generate_round_btn.clicked.connect(self.update_irs)
        
        # Horizontal layout with 4:1 ratio (4/5 for group box, 1/5 for button)
        style_and_generate_layout = QHBoxLayout()
        style_and_generate_layout.addWidget(style_group_box, 4)
        style_and_generate_layout.addWidget(self.generate_round_btn, 1)
        style_and_generate_layout.setAlignment(Qt.AlignVCenter)
        main_layout.addLayout(style_and_generate_layout)

        # --- Main Splitter ---
        self.ir_selection_group = QGroupBox("Select IRs")
        self.ir_selection_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        ir_selection_layout = QVBoxLayout()
        self.checkbox_grid = QGridLayout()
        ir_selection_layout.addLayout(self.checkbox_grid)
        self.ir_selection_group.setLayout(ir_selection_layout)
        detail_group = QGroupBox("IR Detail Preview")
        detail_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        detail_layout = QVBoxLayout()
        self.detail_display = IRDetailDisplay(self)
        detail_layout.addWidget(self.detail_display)
        detail_group.setLayout(detail_layout)
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.ir_selection_group)
        splitter.addWidget(detail_group)
        # Set fixed ratio: left section 1/4, right section 3/4
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 3)
        main_layout.addWidget(splitter)

        # --- Export and Preview Controls (Preview left, Export right) ---
        controls_layout = QHBoxLayout()

        # Preview Controls (left)
        preview_group = QGroupBox("Preview Controls")
        preview_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        preview_layout = QHBoxLayout()
        self.previewCombinedBtn = QPushButton("Preview Combined")
        self.previewCombinedBtn.setToolTip("Play a combined preview of the selected IRs.")
        self.stopPreviewBtn = QPushButton("Stop Preview")
        self.stopPreviewBtn.setToolTip("Stop the audio preview.")
        for btn in (self.previewCombinedBtn, self.stopPreviewBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.previewCombinedBtn.clicked.connect(self.preview_combined_irs)
        self.stopPreviewBtn.clicked.connect(self.stop_preview)
        preview_layout.addWidget(self.previewCombinedBtn)
        preview_layout.addWidget(self.stopPreviewBtn)
        preview_group.setLayout(preview_layout)
        controls_layout.addWidget(preview_group)

        # Export Controls (right)
        export_group = QGroupBox("Export Controls")
        export_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        export_layout = QHBoxLayout()
        self.saveSelectedBtn = QPushButton("Export Selected IRs to WAV")
        self.saveSelectedBtn.setToolTip("Export the selected IRs as individual WAV files.")
        self.saveAllBtn = QPushButton("Export All IRs to WAV")
        self.saveAllBtn.setToolTip("Export all generated IRs as individual WAV files.")
        self.exportCombinedBtn = QPushButton("Export Combined IR to WAV")
        self.exportCombinedBtn.setToolTip("Export a single WAV file that is the combined average of the selected IRs.")
        for btn in (self.saveSelectedBtn, self.saveAllBtn, self.exportCombinedBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.saveSelectedBtn.clicked.connect(self.save_selected_irs)
        self.saveAllBtn.clicked.connect(self.save_all_irs)
        self.exportCombinedBtn.clicked.connect(self.export_combined_irs)
        export_layout.addWidget(self.saveSelectedBtn)
        export_layout.addWidget(self.saveAllBtn)
        export_layout.addWidget(self.exportCombinedBtn)
        export_group.setLayout(export_layout)
        controls_layout.addWidget(export_group)

        main_layout.addLayout(controls_layout)
        
        # --- Bottom section with copyright ---
        copyright_label = QLabel()
        copyright_label.setTextFormat(Qt.RichText)
        copyright_label.setText(
            '© 2025 <a href="https://developdevice.com" style="color: #FFFFFF; text-decoration: none;">Develop Device</a>. All rights reserved.'
        )
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("font-size: 12px; color: #FFFFFF;")
        copyright_label.setOpenExternalLinks(True)
        main_layout.addWidget(copyright_label)
        
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # Initialize the IR processor with the dataset
        try:
            self.processor = IRProcessor()
            self.update_irs()
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR processor: {str(e)}")
            logging.error(f"Failed to initialize IR processor: {e}")

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def update_style(self, style):
        if self.sender().isChecked():
            self.selected_style = style

    def update_irs(self):
        # Use default diversity = 1.0 and seed = None
        diversity = 1.0
        seed = None

        if not hasattr(self, 'processor'):
            self.show_message("Error", "IR processor not initialized")
            return

        self.generate_round_btn.setEnabled(False)
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(processor=self.processor, diversity=diversity, style=self.selected_style, count=10, seed=seed)
        self.worker.moveToThread(self.worker_thread)
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        self.worker_thread.start()

    @pyqtSlot(list)
    def on_generation_finished(self, irs):
        self.last_generated_irs = irs
        self.update_ir_list()
        self.generate_round_btn.setEnabled(True)
        logging.info("IR generation completed.")

    @pyqtSlot(str)
    def on_generation_error(self, error_message):
        self.show_message("Error", f"Error during IR generation: {error_message}")
        self.generate_round_btn.setEnabled(True)

    def update_ir_list(self):
        for i in reversed(range(self.checkbox_grid.count())):
            widget = self.checkbox_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.ir_widgets = []
        total = len(self.last_generated_irs)
        rows = 5
        cols = (total + rows - 1) // rows
        for i in range(total):
            container = QWidget()
            h_layout = QHBoxLayout(container)
            checkbox = DarkCheckBox(f"IR {i+1}")
            checkbox.setToolTip(f"Select IR {i+1}")
            preview_btn = QPushButton("Preview")
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #000000;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #F0F0F0;
                }
                QPushButton:pressed {
                    background-color: #E0E0E0;
                }
            """)
            preview_btn.setToolTip("Play audio preview of this impulse response")
            preview_btn.clicked.connect(lambda checked, idx=i: self.preview_single_ir(idx))
            h_layout.addWidget(checkbox)
            h_layout.addStretch()
            h_layout.addWidget(preview_btn)
            row = i % rows
            col = i // rows
            self.checkbox_grid.addWidget(container, row, col)
            self.ir_widgets.append({'checkbox': checkbox, 'preview_button': preview_btn})

    def save_selected_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "No IR selected for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in selected_indices:
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "Selected IRs were successfully exported as WAV files.")

    def save_all_irs(self):
        if not self.last_generated_irs:
            self.show_message("Warning", "No IR available for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in range(len(self.last_generated_irs)):
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "All IRs were successfully exported as WAV files.")

    def export_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if len(selected_indices) < 1:
            self.show_message("Warning", "Select at least one IR to export combined IR.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export combined IR")
        if not folder:
            return
        base_filename = "Combined_IR.wav"
        filename = self.get_unique_filename(folder, base_filename)
        try:
            sf.write(filename, combined_ir, 48000, subtype='PCM_24')
        except Exception as e:
            self.show_message("Error", f"Error exporting combined IR: {str(e)}")
            return
        self.show_message("Success", "Combined IR was successfully exported as a WAV file.")

    def preview_single_ir(self, idx):
        if idx < 0 or idx >= len(self.last_generated_irs):
            self.show_message("Warning", "The selected IR is not valid.")
            return
        ir = self.last_generated_irs[idx]
        self._preview_ir(ir)
        self.detail_display.update_detail(ir)

    def preview_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "Select at least one IR for combined preview.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        self._preview_ir(combined_ir)
        self.detail_display.update_detail(combined_ir)

    def _preview_ir(self, ir):
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            sample_data, sample_rate = sf.read(sample_path)
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return
        convolved = fftconvolve(sample_data, ir, mode="full")
        convolved = convolved / (np.max(np.abs(convolved)) + 1e-9) * 0.9
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()
        try:
            sf.write(temp_filename, convolved, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return
        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def _on_media_status_changed(self, status):
        if status in (QMediaPlayer.EndOfMedia, QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia):
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def stop_preview(self):
        self.player.stop()

    def show_message(self, title, message):
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("QMessageBox {background-color: #121212; color: #FFFFFF;} QMessageBox QLabel {color: #FFFFFF;} QPushButton { background-color: #333333; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; font-weight: bold; font-size: 14px; padding: 8px 12px;} QPushButton:hover {background-color: #444444;} QPushButton:pressed { background-color: #555555;}")
        msg_box.exec_()

    def closeEvent(self, event):
        if self.worker_thread is not None:
            try:
                if self.worker_thread.isRunning():
                    self.worker_thread.quit()
                    self.worker_thread.wait()
            except RuntimeError:
                pass
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Load SpaceGrotesk fonts
    regular_font_path = os.path.join(os.path.dirname(__file__), "SpaceGrotesk-Regular.ttf")
    bold_font_path = os.path.join(os.path.dirname(__file__), "SpaceGrotesk-Bold.ttf")

    regular_font_id = QFontDatabase.addApplicationFont(regular_font_path)
    bold_font_id = QFontDatabase.addApplicationFont(bold_font_path)

    if regular_font_id != -1 and bold_font_id != -1:
        families = QFontDatabase.applicationFontFamilies(regular_font_id)
        if families:
            default_font = QFont(families[0])
            app.setFont(default_font)
            print("Loaded fonts: SpaceGrotesk Regular and Bold")
        else:
            print("Failed to get SpaceGrotesk font families")
    else:
        print("Failed to load SpaceGrotesk fonts")

    window = IRGeneratorGUI()
    window.show()
    sys.exit(app.exec_())
