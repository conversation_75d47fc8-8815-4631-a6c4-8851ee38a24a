import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap
import logging
from ir_dataset import IRDatasetManager

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# --- CarbonWidget: Custom widget with carbon background ---
class CarbonWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pattern = self.createCarbonPattern()

    def createCarbonPattern(self):
        # Create a 40x40 pixmap with a carbon-like pattern
        pixmap = QPixmap(40, 40)
        pixmap.fill(QColor("#2b2b2b"))  # Base dark color
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        whitePen = QPen(QColor(255, 255, 255, 15))  # ~6% opacity
        blackPen = QPen(QColor(0, 0, 0, 15))
        # Draw white lines
        painter.setPen(whitePen)
        painter.drawLine(0, 10, 30, 40)
        painter.drawLine(10, 0, 40, 30)
        # Draw black lines
        painter.setPen(blackPen)
        painter.drawLine(0, 20, 20, 40)
        painter.drawLine(20, 0, 40, 20)
        painter.end()
        return pixmap

    def paintEvent(self, event):
        painter = QPainter(self)
        brush = QBrush(self.pattern)
        painter.fillRect(self.rect(), brush)
        super().paintEvent(event)

# --- Custom Checkbox without SVG ---
class DarkCheckBox(QCheckBox):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #FFFFFF;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #555555;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #007ACC;
                background-color: #007ACC;
            }
        """)

# --- Simple IR Detail Display ---
class IRDetailDisplay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("background-color: #121212; color: #FFFFFF;")
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Create layout
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Title
        title_label = QLabel("IR Analysis")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #FFFFFF; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Create info display area
        self.info_widget = QWidget()
        self.info_widget.setStyleSheet("background-color: #2b2b2b; border: 1px solid #555555; border-radius: 5px; padding: 15px;")
        info_layout = QVBoxLayout()

        # IR Properties section
        properties_label = QLabel("IR Properties")
        properties_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #007ACC; margin-bottom: 8px;")
        info_layout.addWidget(properties_label)

        self.properties_text = QLabel("No IR selected")
        self.properties_text.setStyleSheet("font-size: 14px; color: #FFFFFF; line-height: 1.4;")
        self.properties_text.setWordWrap(True)
        info_layout.addWidget(self.properties_text)

        # Frequency Analysis section
        freq_label = QLabel("Frequency Analysis")
        freq_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #007ACC; margin: 15px 0 8px 0;")
        info_layout.addWidget(freq_label)

        self.frequency_text = QLabel("No analysis available")
        self.frequency_text.setStyleSheet("font-size: 14px; color: #FFFFFF; line-height: 1.4;")
        self.frequency_text.setWordWrap(True)
        info_layout.addWidget(self.frequency_text)

        # Simple waveform visualization
        waveform_label = QLabel("Waveform Preview")
        waveform_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #007ACC; margin: 15px 0 8px 0;")
        info_layout.addWidget(waveform_label)

        self.waveform_display = QLabel()
        self.waveform_display.setMinimumHeight(100)
        self.waveform_display.setStyleSheet("background-color: #1a1a1a; border: 1px solid #444444; border-radius: 3px;")
        self.waveform_display.setAlignment(Qt.AlignCenter)
        self.waveform_display.setText("No waveform data")
        info_layout.addWidget(self.waveform_display)

        self.info_widget.setLayout(info_layout)
        layout.addWidget(self.info_widget)

        layout.addStretch()
        self.setLayout(layout)

    def update_detail(self, ir, sample_rate=48000):
        """Update the display with IR analysis information"""
        try:
            # Basic IR properties
            duration = len(ir) / sample_rate
            max_amplitude = np.max(np.abs(ir))
            rms_level = np.sqrt(np.mean(ir**2))

            properties_info = f"""
Length: {len(ir)} samples
Duration: {duration:.3f} seconds
Sample Rate: {sample_rate} Hz
Peak Amplitude: {max_amplitude:.6f}
RMS Level: {rms_level:.6f}
Dynamic Range: {20 * np.log10(max_amplitude / (rms_level + 1e-9)):.1f} dB
            """.strip()

            self.properties_text.setText(properties_info)

            # Frequency analysis
            n = len(ir)
            freqs = np.fft.rfftfreq(n, d=1/sample_rate)
            IR_fft = np.fft.rfft(ir)
            mag = np.abs(IR_fft)

            # Find dominant frequencies
            mag_db = 20 * np.log10(mag + 1e-9)
            peak_indices = np.argsort(mag_db)[-5:]  # Top 5 peaks
            dominant_freqs = freqs[peak_indices]
            dominant_mags = mag_db[peak_indices]

            # Frequency band analysis
            low_band = np.mean(mag_db[(freqs >= 20) & (freqs < 300)])
            mid_band = np.mean(mag_db[(freqs >= 300) & (freqs < 3000)])
            high_band = np.mean(mag_db[(freqs >= 3000) & (freqs < sample_rate/2)])

            freq_info = f"""
Low Band (20-300 Hz): {low_band:.1f} dB
Mid Band (300-3000 Hz): {mid_band:.1f} dB
High Band (3000+ Hz): {high_band:.1f} dB

Dominant Frequencies:
            """.strip()

            for i in range(min(3, len(dominant_freqs))):
                freq_info += f"\n{dominant_freqs[-(i+1)]:.0f} Hz ({dominant_mags[-(i+1)]:.1f} dB)"

            self.frequency_text.setText(freq_info)

            # Simple ASCII waveform visualization
            self._create_waveform_display(ir)

        except Exception as e:
            self.properties_text.setText(f"Error analyzing IR: {str(e)}")
            self.frequency_text.setText("Analysis failed")
            self.waveform_display.setText("Waveform display error")

    def _create_waveform_display(self, ir):
        """Create a simple ASCII-style waveform display"""
        try:
            # Downsample for display
            display_length = 80
            step = max(1, len(ir) // display_length)
            downsampled = ir[::step][:display_length]

            # Normalize to display range
            if np.max(np.abs(downsampled)) > 0:
                normalized = downsampled / np.max(np.abs(downsampled))
            else:
                normalized = downsampled

            # Create ASCII waveform
            height = 20
            waveform_lines = []

            for y in range(height):
                line = ""
                threshold = 1.0 - (2.0 * y / (height - 1))  # From 1 to -1

                for x in range(len(normalized)):
                    if abs(normalized[x] - threshold) < 0.1:
                        line += "█"
                    elif abs(normalized[x]) > abs(threshold):
                        line += "│"
                    else:
                        line += " "

                waveform_lines.append(line)

            waveform_text = "\n".join(waveform_lines)
            self.waveform_display.setText(f"<pre style='color: #00FF00; font-family: monospace; font-size: 8px;'>{waveform_text}</pre>")

        except Exception as e:
            self.waveform_display.setText(f"Waveform error: {str(e)}")

# --- IRProcessor ---
class IRProcessor:
    def __init__(self):
        self.dataset_manager = IRDatasetManager()  # Uses bundled data
        self.ir_length = 2048
        self.sample_rate = 48000

        logging.info(f"Initialized IR processor with {self.dataset_manager.get_ir_count()} bundled IR files")
        style_counts = self.dataset_manager.get_style_counts()
        for style, count in style_counts.items():
            logging.info(f"  {style}: {count} files")

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """Generate multiple IRs using the dataset-based approach"""
        try:
            irs = self.dataset_manager.generate_multiple_irs(
                diversity=diversity,
                count=count,
                style=style
            )
            logging.info(f"Generated {len(irs)} IRs with style '{style}' and diversity {diversity}")
            return irs
        except Exception as e:
            logging.error(f"Error generating IRs: {e}")
            # Return fallback IRs
            fallback_irs = []
            for i in range(count):
                fallback_ir = np.zeros(self.ir_length)
                fallback_ir[0] = 1.0  # Simple impulse
                fallback_irs.append(fallback_ir)
            return fallback_irs

# --- IR Generation Worker ---
class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, processor, diversity, style, count, seed):
        super().__init__()
        self.processor = processor
        self.diversity = diversity
        self.style = style
        self.count = count
        self.seed = seed

    @pyqtSlot()
    def run(self):
        try:
            if self.seed is not None:
                np.random.seed(self.seed)
            logging.info("Starting IR generation with diversity=%s, style=%s, count=%d", self.diversity, self.style, self.count)
            irs = self.processor.generate_multiple_irs(diversity=self.diversity, count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            logging.error("Error during IR generation: %s", str(e))
            self.error.emit(str(e))

# --- Main GUI Class ---
class IRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist")
        self.setGeometry(100, 100, 1400, 950)
        self.last_generated_irs = None
        self.ir_widgets = []
        self.selected_style = "American"
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # --- Central widget with carbon background ---
        main_widget = CarbonWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # --- Header Section ---
        header_layout = QVBoxLayout()
        title_layout = QHBoxLayout()
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet("font-size: 48px; font-weight: bold; color: #FFFFFF;")
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setOffset(5, 5)
        shadow.setColor(QColor(0, 0, 0, 220))
        title_label.setGraphicsEffect(shadow)
        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet("font-size: 24px; font-style: italic; color: #AAAAAA; margin-left: 10px;")
        title_layout.addStretch()
        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)
        
        # Add subheading
        subheading_label = QLabel("Advanced Cabinet IR Generator for Guitarists Powered by AI")
        subheading_label.setStyleSheet("font-size: 20px; color: #FFFFFF;")
        subheading_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subheading_label)
        
        header_layout.addSpacing(10)
        instructions = QLabel(
            "Select the IR sound characteristic below and click the 'Generate IRs' button to create 10 new cabinet IRs.\n"
            "Use the IR list on the left to select and preview individual IRs. The detail panel on the right displays the EQ curve and spectrogram of the selected IR."
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("font-size: 16px; color: #EEEEEE;")
        instructions.setWordWrap(True)
        header_layout.addWidget(instructions)
        main_layout.addLayout(header_layout)

        # --- Combined IR Sound Characteristic selection and Generate Button ---
        # Create a horizontal layout with fixed height of 50 for both parts.
        style_group_box = QGroupBox("Select IR Sound Characteristic")
        style_group_box.setStyleSheet("""
            QGroupBox { 
                background-color: #444444; 
                font-size: 18px; 
                font-weight: bold; 
                color: #FFFFFF; 
                border: 2px solid #3F6AC2; 
                border-radius: 5px; 
                margin-top: 0.5em;
            } 
            QGroupBox::title { 
                subcontrol-origin: margin; 
                subcontrol-position: top center; 
                padding: 0 3px; 
                color: #FFFFFF; 
            }
        """)
        style_layout = QHBoxLayout()
        self.style_button_group = QButtonGroup()
        # Radio buttons in order: American, British, German, Random
        self.radio_american = QRadioButton("American")
        self.radio_british = QRadioButton("British")
        self.radio_german = QRadioButton("German")
        self.radio_custom = QRadioButton("Random")
        self.radio_american.setChecked(True)
        self.radio_american.setToolTip("Select the American cabinet IR style.")
        self.radio_british.setToolTip("Select the British cabinet IR style.")
        self.radio_german.setToolTip("Select the German cabinet IR style.")
        self.radio_custom.setToolTip("Select a random cabinet IR style.")
        for rb in (self.radio_american, self.radio_british, self.radio_german, self.radio_custom):
            rb.setStyleSheet("QRadioButton { font-size: 16px; color: #FFFFFF; } QRadioButton::indicator { width: 18px; height: 18px; } QRadioButton::indicator:unchecked { border: 1px solid #555555; background-color: #333333; border-radius: 9px; } QRadioButton::indicator:checked { border: 1px solid #007ACC; background-color: #007ACC; border-radius: 9px; }")
            self.style_button_group.addButton(rb)
        self.radio_american.toggled.connect(lambda: self.update_style("American"))
        self.radio_british.toggled.connect(lambda: self.update_style("British"))
        self.radio_german.toggled.connect(lambda: self.update_style("German"))
        self.radio_custom.toggled.connect(lambda: self.update_style("Random"))
        style_layout.addWidget(self.radio_american)
        style_layout.addWidget(self.radio_british)
        style_layout.addWidget(self.radio_german)
        style_layout.addWidget(self.radio_custom)
        style_group_box.setLayout(style_layout)
        style_group_box.setFixedHeight(50)

        self.generate_round_btn = QPushButton("Click here to generate 10 new cabinet IRs")
        self.generate_round_btn.setFixedHeight(50)
        self.generate_round_btn.setToolTip("Generate 10 new cabinet impulse responses using AI.")
        self.generate_round_btn.setStyleSheet("""
            QPushButton {
                background-color: #5481EA;
                border: 2px solid #3F6AC2;
                border-radius: 10px;
                font-size: 18px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #6B9DFA;
            }
            QPushButton:pressed {
                background-color: #4B78D0;
                border-style: inset;
            }
            QPushButton:disabled {
                background-color: #666666;
                border: 2px solid #555555;
                color: #AAAAAA;
            }
        """)
        self.generate_round_btn.clicked.connect(self.update_irs)
        
        # Horizontal layout with 4:1 ratio (4/5 for group box, 1/5 for button)
        style_and_generate_layout = QHBoxLayout()
        style_and_generate_layout.addWidget(style_group_box, 4)
        style_and_generate_layout.addWidget(self.generate_round_btn, 1)
        style_and_generate_layout.setAlignment(Qt.AlignVCenter)
        main_layout.addLayout(style_and_generate_layout)

        # --- Main Splitter ---
        self.ir_selection_group = QGroupBox("Select IRs")
        self.ir_selection_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        ir_selection_layout = QVBoxLayout()
        self.checkbox_grid = QGridLayout()
        ir_selection_layout.addLayout(self.checkbox_grid)
        self.ir_selection_group.setLayout(ir_selection_layout)
        detail_group = QGroupBox("IR Detail Preview")
        detail_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        detail_layout = QVBoxLayout()
        self.detail_display = IRDetailDisplay(self)
        detail_layout.addWidget(self.detail_display)
        detail_group.setLayout(detail_layout)
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.ir_selection_group)
        splitter.addWidget(detail_group)
        # Set fixed ratio: left section 1/4, right section 3/4
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 3)
        main_layout.addWidget(splitter)

        # --- Export and Preview Controls (Preview left, Export right) ---
        controls_layout = QHBoxLayout()

        # Preview Controls (left)
        preview_group = QGroupBox("Preview Controls")
        preview_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        preview_layout = QHBoxLayout()
        self.previewCombinedBtn = QPushButton("Preview Combined")
        self.previewCombinedBtn.setToolTip("Play a combined preview of the selected IRs.")
        self.stopPreviewBtn = QPushButton("Stop Preview")
        self.stopPreviewBtn.setToolTip("Stop the audio preview.")
        for btn in (self.previewCombinedBtn, self.stopPreviewBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.previewCombinedBtn.clicked.connect(self.preview_combined_irs)
        self.stopPreviewBtn.clicked.connect(self.stop_preview)
        preview_layout.addWidget(self.previewCombinedBtn)
        preview_layout.addWidget(self.stopPreviewBtn)
        preview_group.setLayout(preview_layout)
        controls_layout.addWidget(preview_group)

        # Export Controls (right)
        export_group = QGroupBox("Export Controls")
        export_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        export_layout = QHBoxLayout()
        self.saveSelectedBtn = QPushButton("Export Selected IRs to WAV")
        self.saveSelectedBtn.setToolTip("Export the selected IRs as individual WAV files.")
        self.saveAllBtn = QPushButton("Export All IRs to WAV")
        self.saveAllBtn.setToolTip("Export all generated IRs as individual WAV files.")
        self.exportCombinedBtn = QPushButton("Export Combined IR to WAV")
        self.exportCombinedBtn.setToolTip("Export a single WAV file that is the combined average of the selected IRs.")
        for btn in (self.saveSelectedBtn, self.saveAllBtn, self.exportCombinedBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.saveSelectedBtn.clicked.connect(self.save_selected_irs)
        self.saveAllBtn.clicked.connect(self.save_all_irs)
        self.exportCombinedBtn.clicked.connect(self.export_combined_irs)
        export_layout.addWidget(self.saveSelectedBtn)
        export_layout.addWidget(self.saveAllBtn)
        export_layout.addWidget(self.exportCombinedBtn)
        export_group.setLayout(export_layout)
        controls_layout.addWidget(export_group)

        main_layout.addLayout(controls_layout)
        
        # --- Bottom section with copyright ---
        copyright_label = QLabel()
        copyright_label.setTextFormat(Qt.RichText)
        copyright_label.setText(
            '© 2025 <a href="https://developdevice.com" style="color: #FFFFFF; text-decoration: none;">Develop Device</a>. All rights reserved.'
        )
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("font-size: 12px; color: #FFFFFF;")
        copyright_label.setOpenExternalLinks(True)
        main_layout.addWidget(copyright_label)
        
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # Initialize the IR processor with the dataset
        try:
            self.processor = IRProcessor()
            self.update_irs()
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR processor: {str(e)}")
            logging.error(f"Failed to initialize IR processor: {e}")

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def update_style(self, style):
        if self.sender().isChecked():
            self.selected_style = style

    def update_irs(self):
        # Use default diversity = 1.0 and seed = None
        diversity = 1.0
        seed = None

        if not hasattr(self, 'processor'):
            self.show_message("Error", "IR processor not initialized")
            return

        self.generate_round_btn.setEnabled(False)
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(processor=self.processor, diversity=diversity, style=self.selected_style, count=10, seed=seed)
        self.worker.moveToThread(self.worker_thread)
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        self.worker_thread.start()

    @pyqtSlot(list)
    def on_generation_finished(self, irs):
        self.last_generated_irs = irs
        self.update_ir_list()
        self.generate_round_btn.setEnabled(True)
        logging.info("IR generation completed.")

    @pyqtSlot(str)
    def on_generation_error(self, error_message):
        self.show_message("Error", f"Error during IR generation: {error_message}")
        self.generate_round_btn.setEnabled(True)

    def update_ir_list(self):
        for i in reversed(range(self.checkbox_grid.count())):
            widget = self.checkbox_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.ir_widgets = []
        total = len(self.last_generated_irs)
        rows = 5
        cols = (total + rows - 1) // rows
        for i in range(total):
            container = QWidget()
            h_layout = QHBoxLayout(container)
            checkbox = DarkCheckBox(f"IR {i+1}")
            checkbox.setToolTip(f"Select IR {i+1}")
            preview_btn = QPushButton("Preview")
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #000000;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #F0F0F0;
                }
                QPushButton:pressed {
                    background-color: #E0E0E0;
                }
            """)
            preview_btn.setToolTip("Play audio preview of this impulse response")
            preview_btn.clicked.connect(lambda checked, idx=i: self.preview_single_ir(idx))
            h_layout.addWidget(checkbox)
            h_layout.addStretch()
            h_layout.addWidget(preview_btn)
            row = i % rows
            col = i // rows
            self.checkbox_grid.addWidget(container, row, col)
            self.ir_widgets.append({'checkbox': checkbox, 'preview_button': preview_btn})

    def save_selected_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "No IR selected for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in selected_indices:
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "Selected IRs were successfully exported as WAV files.")

    def save_all_irs(self):
        if not self.last_generated_irs:
            self.show_message("Warning", "No IR available for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in range(len(self.last_generated_irs)):
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "All IRs were successfully exported as WAV files.")

    def export_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if len(selected_indices) < 1:
            self.show_message("Warning", "Select at least one IR to export combined IR.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export combined IR")
        if not folder:
            return
        base_filename = "Combined_IR.wav"
        filename = self.get_unique_filename(folder, base_filename)
        try:
            sf.write(filename, combined_ir, 48000, subtype='PCM_24')
        except Exception as e:
            self.show_message("Error", f"Error exporting combined IR: {str(e)}")
            return
        self.show_message("Success", "Combined IR was successfully exported as a WAV file.")

    def preview_single_ir(self, idx):
        if idx < 0 or idx >= len(self.last_generated_irs):
            self.show_message("Warning", "The selected IR is not valid.")
            return
        ir = self.last_generated_irs[idx]
        self._preview_ir(ir)
        self.detail_display.update_detail(ir)

    def preview_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "Select at least one IR for combined preview.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        self._preview_ir(combined_ir)
        self.detail_display.update_detail(combined_ir)

    def _preview_ir(self, ir):
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            sample_data, sample_rate = sf.read(sample_path)
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return
        convolved = fftconvolve(sample_data, ir, mode="full")
        convolved = convolved / (np.max(np.abs(convolved)) + 1e-9) * 0.9
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()
        try:
            sf.write(temp_filename, convolved, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return
        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def _on_media_status_changed(self, status):
        if status in (QMediaPlayer.EndOfMedia, QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia):
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def stop_preview(self):
        self.player.stop()

    def show_message(self, title, message):
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("QMessageBox {background-color: #121212; color: #FFFFFF;} QMessageBox QLabel {color: #FFFFFF;} QPushButton { background-color: #333333; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; font-weight: bold; font-size: 14px; padding: 8px 12px;} QPushButton:hover {background-color: #444444;} QPushButton:pressed { background-color: #555555;}")
        msg_box.exec_()

    def closeEvent(self, event):
        if self.worker_thread is not None:
            try:
                if self.worker_thread.isRunning():
                    self.worker_thread.quit()
                    self.worker_thread.wait()
            except RuntimeError:
                pass
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    font_path = os.path.join(os.path.dirname(__file__), "Exo-VariableFont_wght.ttf")
    font_id = QFontDatabase.addApplicationFont(font_path)
    if font_id != -1:
        families = QFontDatabase.applicationFontFamilies(font_id)
        if families:
            default_font = QFont(families[0])
            app.setFont(default_font)
            print("Loaded font:", families[0])
    else:
        print("Failed to load Exo-VariableFont_wght.ttf")
    window = IRGeneratorGUI()
    window.show()
    sys.exit(app.exec_())
