# -*- mode: python ; coding: utf-8 -*-
# IR-Alchemist PyInstaller Spec File
# Updated for Windows compilation with bundled dataset and SpaceGrotesk fonts

a = Analysis(
    ['IR-Alchemist.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ir_bundle.py', '.'),
        ('ir_dataset.py', '.'),
        ('SpaceGrotesk-Regular.ttf', '.'),
        ('SpaceGrotesk-Bold.ttf', '.'),
        ('Sample.wav', '.'),
        ('IR-Alchemist.ico', '.')
    ],
    hiddenimports=[
        'ir_bundle',
        'ir_dataset',
        'numpy',
        'scipy',
        'soundfile',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtMultimedia'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'torch',
        'tensorflow',
        'keras'
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='IR-Alchemist',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='IR-Alchemist.ico',
    version_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='IR-Alchemist',
)
