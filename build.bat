@echo off
REM IR-Alchemist Windows Build Script
REM This script compiles the IR-Alchemist application for Windows distribution

echo ========================================
echo IR-Alchemist Windows Build Script
echo ========================================
echo.

REM Check if PyInstaller is installed
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ERROR: PyInstaller is not installed.
    echo Please install it with: pip install pyinstaller
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
echo Checking required files...
if not exist "IR-Alchemist.py" (
    echo ERROR: IR-Alchemist.py not found
    pause
    exit /b 1
)
if not exist "ir_bundle.py" (
    echo ERROR: ir_bundle.py not found
    pause
    exit /b 1
)
if not exist "ir_dataset.py" (
    echo ERROR: ir_dataset.py not found
    pause
    exit /b 1
)
if not exist "SpaceGrotesk-Regular.ttf" (
    echo ERROR: SpaceGrotesk-Regular.ttf not found
    pause
    exit /b 1
)
if not exist "SpaceGrotesk-Bold.ttf" (
    echo ERROR: SpaceGrotesk-Bold.ttf not found
    pause
    exit /b 1
)
if not exist "Sample.wav" (
    echo ERROR: Sample.wav not found
    pause
    exit /b 1
)
if not exist "IR-Alchemist.ico" (
    echo ERROR: IR-Alchemist.ico not found
    pause
    exit /b 1
)
echo All required files found!
echo.

REM Clean previous build
echo Cleaning previous build...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
echo.

REM Build the application
echo Starting PyInstaller build...
echo This may take several minutes...
echo.
pyinstaller IR-Alchemist.spec

REM Check if build was successful
if exist "dist\IR-Alchemist\IR-Alchemist.exe" (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The compiled application is located at:
    echo dist\IR-Alchemist\IR-Alchemist.exe
    echo.
    echo You can now distribute the entire "dist\IR-Alchemist" folder.
    echo.
    echo Build contents:
    dir "dist\IR-Alchemist" /b
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo.
)

pause
