#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IR Dataset Manager for IR-Alchemist
Handles loading, processing, and generating IRs from pre-existing IR files
"""

import os
import random
import numpy as np
import soundfile as sf
from scipy.signal import butter, lfilter, resample
from pathlib import Path
import logging

class IRDatasetManager:
    """Manages a collection of IR files and provides methods to generate new IRs"""
    
    def __init__(self, ir_folder="IR-Alchemist IRs", target_sample_rate=48000, target_length=2048):
        self.ir_folder = ir_folder
        self.target_sample_rate = target_sample_rate
        self.target_length = target_length
        self.ir_files = []
        self.loaded_irs = {}
        self.style_categories = {
            "American": [],
            "British": [],
            "German": [],
            "Random": []
        }
        
        # Load and categorize IR files
        self._load_ir_files()
        self._categorize_irs()
        
        logging.info(f"Loaded {len(self.ir_files)} IR files from {ir_folder}")
    
    def _load_ir_files(self):
        """Load all IR files from the specified folder"""
        if not os.path.exists(self.ir_folder):
            raise FileNotFoundError(f"IR folder '{self.ir_folder}' not found")
        
        self.ir_files = []
        for file_path in Path(self.ir_folder).glob("*.wav"):
            if file_path.is_file():
                self.ir_files.append(str(file_path))
        
        if not self.ir_files:
            raise ValueError(f"No WAV files found in '{self.ir_folder}'")
        
        self.ir_files.sort()  # Sort for consistent ordering
    
    def _categorize_irs(self):
        """Categorize IRs into different styles based on filename or characteristics"""
        # Simple categorization based on file index for now
        # In a real implementation, you might analyze frequency characteristics
        total_files = len(self.ir_files)
        
        # Distribute files across categories
        for i, ir_file in enumerate(self.ir_files):
            category_index = i % 4
            if category_index == 0:
                self.style_categories["American"].append(ir_file)
            elif category_index == 1:
                self.style_categories["British"].append(ir_file)
            elif category_index == 2:
                self.style_categories["German"].append(ir_file)
            else:
                self.style_categories["Random"].append(ir_file)
        
        # Ensure all categories have files
        for style in self.style_categories:
            if not self.style_categories[style]:
                self.style_categories[style] = self.ir_files[:10]  # Use first 10 as fallback
    
    def _load_and_process_ir(self, ir_path):
        """Load and process a single IR file"""
        if ir_path in self.loaded_irs:
            return self.loaded_irs[ir_path]
        
        try:
            # Load the IR file
            data, sr = sf.read(ir_path)
            
            # Convert to mono if stereo
            if len(data.shape) > 1:
                data = np.mean(data, axis=1)
            
            # Resample if necessary
            if sr != self.target_sample_rate:
                data = resample(data, int(len(data) * self.target_sample_rate / sr))
            
            # Trim or pad to target length
            if len(data) > self.target_length:
                data = data[:self.target_length]
            elif len(data) < self.target_length:
                data = np.pad(data, (0, self.target_length - len(data)))
            
            # Normalize
            max_val = np.max(np.abs(data))
            if max_val > 0:
                data = data / max_val
            
            # Cache the processed IR
            self.loaded_irs[ir_path] = data
            return data
            
        except Exception as e:
            logging.error(f"Error loading IR file {ir_path}: {e}")
            # Return a simple impulse as fallback
            fallback = np.zeros(self.target_length)
            fallback[0] = 1.0
            return fallback
    
    def apply_style_processing(self, ir, style):
        """Apply style-specific processing to an IR"""
        # Create frequency domain representation
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/self.target_sample_rate)
        
        # Apply style-specific frequency shaping
        if style == "American":
            # Boost low frequencies slightly
            boost = np.ones_like(IR_fft)
            boost[freqs < 300] *= 1.1
            IR_fft *= boost
        elif style == "British":
            # Boost mid frequencies
            boost = np.ones_like(IR_fft)
            boost[(freqs >= 300) & (freqs <= 800)] *= 1.15
            IR_fft *= boost
        elif style == "German":
            # Boost high frequencies
            boost = np.ones_like(IR_fft)
            boost[(freqs >= 4000) & (freqs <= 6000)] *= 1.1
            IR_fft *= boost
        elif style == "Random":
            # Apply random modulation
            boost = np.random.uniform(0.9, 1.1, size=IR_fft.shape)
            IR_fft *= boost
        
        # Convert back to time domain
        processed_ir = np.fft.irfft(IR_fft, n=n)
        
        # Normalize
        max_val = np.max(np.abs(processed_ir))
        if max_val > 0:
            processed_ir = processed_ir / max_val
        
        return processed_ir
    
    def apply_variation(self, ir, diversity=1.0):
        """Apply random variations to an IR to create diversity"""
        if diversity <= 0:
            return ir
        
        # Apply random time shift
        if diversity > 0.3:
            shift_samples = int(np.random.uniform(-5, 5) * diversity)
            if shift_samples != 0:
                ir = np.roll(ir, shift_samples)
        
        # Apply random amplitude modulation
        if diversity > 0.2:
            amp_mod = np.random.uniform(0.9, 1.1) * diversity + (1.0 - diversity)
            ir = ir * amp_mod
        
        # Apply subtle frequency modulation
        if diversity > 0.5:
            n = len(ir)
            IR_fft = np.fft.rfft(ir)
            freqs = np.fft.rfftfreq(n, d=1/self.target_sample_rate)
            
            # Random frequency response variation
            variation = np.random.uniform(0.95, 1.05, size=IR_fft.shape)
            variation = np.convolve(variation, np.ones(3)/3, mode='same')  # Smooth
            IR_fft *= variation * diversity + (1.0 - diversity)
            
            ir = np.fft.irfft(IR_fft, n=n)
        
        # Normalize
        max_val = np.max(np.abs(ir))
        if max_val > 0:
            ir = ir / max_val
        
        return ir
    
    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """Generate multiple IRs by selecting and processing existing IR files"""
        generated_irs = []
        
        # Get appropriate IR files for the style
        style_files = self.style_categories.get(style, self.ir_files)
        if not style_files:
            style_files = self.ir_files
        
        for i in range(count):
            # Select a random IR file (with replacement for diversity)
            selected_file = random.choice(style_files)
            
            # Load and process the IR
            base_ir = self._load_and_process_ir(selected_file)
            
            # Apply style-specific processing
            processed_ir = self.apply_style_processing(base_ir.copy(), style)
            
            # Apply random variations for diversity
            varied_ir = self.apply_variation(processed_ir, diversity)
            
            generated_irs.append(varied_ir)
        
        return generated_irs
    
    def get_random_ir(self, style="Random"):
        """Get a single random IR from the dataset"""
        style_files = self.style_categories.get(style, self.ir_files)
        if not style_files:
            style_files = self.ir_files
        
        selected_file = random.choice(style_files)
        return self._load_and_process_ir(selected_file)
    
    def get_ir_count(self):
        """Get the total number of available IR files"""
        return len(self.ir_files)
    
    def get_style_counts(self):
        """Get the number of IRs in each style category"""
        return {style: len(files) for style, files in self.style_categories.items()}
