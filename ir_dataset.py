#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IR Dataset Manager for IR-Al<PERSON>
Handles loading and selecting IRs from bundled dataset without post-processing
Provides unique IR selection within each generation batch
"""

import os
import random
import numpy as np
import soundfile as sf
from scipy.signal import butter, lfilter, resample
from pathlib import Path
import logging
from ir_bundle import load_bundled_irs

class IRDatasetManager:
    """Manages a collection of bundled IR files and provides methods to select unique IRs without post-processing"""
    
    def __init__(self, ir_folder=None, target_sample_rate=48000, target_length=2048):
        self.target_sample_rate = target_sample_rate
        self.target_length = target_length
        self.ir_files = []
        self.loaded_irs = {}
        self.bundled_data = None
        self.style_categories = {
            "American": [],
            "British": [],
            "German": [],
            "Random": []
        }

        # Load bundled IR data
        self._load_bundled_data()
        self._categorize_irs()

        logging.info(f"Loaded {len(self.ir_files)} IR files from bundled dataset")
    
    def _load_bundled_data(self):
        """Load IR data from the bundled format"""
        try:
            self.bundled_data = load_bundled_irs()
            self.ir_files = list(self.bundled_data.keys())
            self.ir_files.sort()  # Sort for consistent ordering

            if not self.ir_files:
                raise ValueError("No IR data found in bundle")

        except Exception as e:
            raise RuntimeError(f"Failed to load bundled IR data: {e}")
    
    def _categorize_irs(self):
        """Categorize IRs into different styles based on filename or characteristics"""
        # Simple categorization based on file index for now
        # In a real implementation, you might analyze frequency characteristics
        total_files = len(self.ir_files)
        
        # Distribute files across categories
        for i, ir_filename in enumerate(self.ir_files):
            category_index = i % 4
            if category_index == 0:
                self.style_categories["American"].append(ir_filename)
            elif category_index == 1:
                self.style_categories["British"].append(ir_filename)
            elif category_index == 2:
                self.style_categories["German"].append(ir_filename)
            else:
                self.style_categories["Random"].append(ir_filename)
        
        # Ensure all categories have files
        for style in self.style_categories:
            if not self.style_categories[style]:
                self.style_categories[style] = self.ir_files[:10]  # Use first 10 as fallback
    
    def _load_and_process_ir(self, ir_filename):
        """Load and process a single IR from bundled data"""
        if ir_filename in self.loaded_irs:
            return self.loaded_irs[ir_filename]

        try:
            # Get IR data from bundle
            if ir_filename not in self.bundled_data:
                raise ValueError(f"IR {ir_filename} not found in bundle")

            ir_info = self.bundled_data[ir_filename]
            data = ir_info['data'].copy()
            sr = ir_info['sample_rate']

            # Data is already mono from bundling process

            # Resample if necessary
            if sr != self.target_sample_rate:
                data = resample(data, int(len(data) * self.target_sample_rate / sr))

            # Trim or pad to target length
            if len(data) > self.target_length:
                data = data[:self.target_length]
            elif len(data) < self.target_length:
                data = np.pad(data, (0, self.target_length - len(data)))

            # Normalize
            max_val = np.max(np.abs(data))
            if max_val > 0:
                data = data / max_val

            # Cache the processed IR
            self.loaded_irs[ir_filename] = data
            return data

        except Exception as e:
            logging.error(f"Error loading IR {ir_filename}: {e}")
            # Return a simple impulse as fallback
            fallback = np.zeros(self.target_length)
            fallback[0] = 1.0
            return fallback
    

    

    
    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """Generate multiple unique IRs by selecting from existing IR files without replacement"""
        generated_irs = []

        # Get appropriate IR files for the style
        style_files = self.style_categories.get(style, self.ir_files)
        if not style_files:
            style_files = self.ir_files

        # Ensure we don't request more IRs than available in the style category
        available_count = len(style_files)
        if count > available_count:
            logging.warning(f"Requested {count} IRs but only {available_count} available in {style} style. Using all available.")
            count = available_count

        # Select unique IR filenames without replacement
        selected_filenames = random.sample(style_files, count)

        for filename in selected_filenames:
            # Load and process the IR (basic processing only: resample, normalize, length adjustment)
            ir = self._load_and_process_ir(filename)
            generated_irs.append(ir)

        logging.info(f"Generated {len(generated_irs)} unique IRs from {style} style (no post-processing applied)")
        return generated_irs
    
    def get_random_ir(self, style="Random"):
        """Get a single random IR from the dataset (no post-processing applied)"""
        style_files = self.style_categories.get(style, self.ir_files)
        if not style_files:
            style_files = self.ir_files

        selected_filename = random.choice(style_files)
        return self._load_and_process_ir(selected_filename)
    
    def get_ir_count(self):
        """Get the total number of available IR files"""
        return len(self.ir_files)
    
    def get_style_counts(self):
        """Get the number of IRs in each style category"""
        return {style: len(files) for style, files in self.style_categories.items()}
