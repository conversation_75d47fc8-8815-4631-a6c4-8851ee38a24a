#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test core functionality without GUI
"""

import sys
import traceback

def test_core_functionality():
    """Test the core IR processing functionality"""
    try:
        print("Testing core functionality...")
        
        # Test dataset manager
        from ir_dataset import IRDatasetManager
        manager = IRDatasetManager('IR-Alchemist IRs')
        print(f"✓ Dataset manager loaded {manager.get_ir_count()} IR files")
        
        # Test style distribution
        style_counts = manager.get_style_counts()
        for style, count in style_counts.items():
            print(f"  {style}: {count} files")
        
        # Test IR generation for each style
        styles = ['American', 'British', 'German', 'Random']
        for style in styles:
            irs = manager.generate_multiple_irs(diversity=0.7, count=2, style=style)
            print(f"✓ Generated {len(irs)} {style} style IRs")
            
            # Verify IR properties
            for i, ir in enumerate(irs):
                if len(ir) != 2048:
                    raise ValueError(f"IR {i+1} has wrong length: {len(ir)}")
                if max(abs(ir)) == 0:
                    raise ValueError(f"IR {i+1} is silent")
        
        # Test diversity levels
        diversities = [0.0, 0.5, 1.0]
        for diversity in diversities:
            irs = manager.generate_multiple_irs(diversity=diversity, count=3, style='American')
            print(f"✓ Generated IRs with diversity {diversity}")
        
        # Test export functionality
        import soundfile as sf
        import tempfile
        import os

        test_ir = manager.get_random_ir('British')
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=True) as tmp:
            sf.write(tmp.name, test_ir, 48000, subtype='PCM_24')
            print(f"✓ Successfully exported test IR")

            # Verify the exported file
            data, sr = sf.read(tmp.name)
            if sr != 48000:
                raise ValueError(f"Wrong sample rate: {sr}")
            if len(data) != 2048:
                raise ValueError(f"Wrong length: {len(data)}")

            print("✓ Export verification successful")
        
        print("✓ All core functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Core functionality error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("IR-Alchemist Core Functionality Test")
    print("=" * 40)
    
    success = test_core_functionality()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ Core functionality works correctly!")
        print("The dataset-based IR generation system is working properly.")
        print("The application should be able to generate IRs from the provided dataset.")
    else:
        print("✗ Core functionality test failed.")
    
    sys.exit(0 if success else 1)
