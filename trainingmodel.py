import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import soundfile as sf
from pathlib import Path
import numpy as np
from torch.amp import autocast, GradScaler
from torch.utils.tensorboard import SummaryWriter
import warnings
warnings.filterwarnings('ignore')

class CabinetIRGenerator(nn.Module):
    def __init__(self, latent_dim=256, device='cpu'):
        super().__init__()
        self.ir_length = 2048
        self.device = device

        self.generator = nn.Sequential(
            nn.Linear(latent_dim, 512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            
            nn.Linear(512, 1024),
            nn.<PERSON><PERSON><PERSON>(0.2),
            nn.BatchNorm1d(1024),
            nn.Dropout(0.2),
            
            nn.Linear(1024, 2048),
            nn.LeakyReLU(0.2),
            nn.BatchNorm1d(2048),
            nn.Dropout(0.2),
            
            nn.Linear(2048, 2048),
            nn.Tanh()
        )
        
        # Parameters for frequency adjustments
        self.bass_emphasis = nn.Parameter(torch.rand(1))
        self.mid_scoop = nn.Parameter(torch.rand(1))
        self.high_presence = nn.Parameter(torch.rand(1))
        
    def forward(self, z, bass=0.5, mids=0.5, highs=0.5):
        ir = self.generator(z)
        
        freq_domain = torch.fft.rfft(ir)
        freqs = torch.fft.rfftfreq(self.ir_length, d=1/48000).to(self.device)

        bass_mask = (freqs < 300).float()
        mids_mask = ((freqs >= 300) & (freqs < 2000)).float()
        highs_mask = (freqs >= 2000).float()
        
        # Apply frequency adjustments using sigmoid activation for stability
        freq_domain = freq_domain * (1 + bass * bass_mask * self.bass_emphasis.sigmoid())
        freq_domain = freq_domain * (1 + mids * mids_mask * self.mid_scoop.sigmoid())
        freq_domain = freq_domain * (1 + highs * highs_mask * self.high_presence.sigmoid())
        
        ir = torch.fft.irfft(freq_domain)
        return ir

class IRDataset(Dataset):
    def __init__(self, folder_path):
        self.ir_files = []
        self.folder_path = folder_path
        self.load_irs()

    def load_irs(self):
        """Load IR files from the given folder."""
        self.ir_files = []
        for ext in ['*.wav', '*.WAV']:
            for file in Path(self.folder_path).rglob(ext):
                if not file.name.startswith('.') and '__MACOSX' not in str(file):
                    self.ir_files.append(file)
        print(f"Found {len(self.ir_files)} valid IR files")

    def __len__(self):
        return len(self.ir_files)
        
    def __getitem__(self, idx):
        ir_path = self.ir_files[idx]
        try:
            with sf.SoundFile(str(ir_path)) as f:
                audio = f.read()
                sr = f.samplerate
            
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)
            
            if len(audio) == 0:
                return torch.zeros(2048)
                
            if np.any(np.isnan(audio)) or np.any(np.isinf(audio)):
                return torch.zeros(2048)
            
            if sr != 48000:
                orig_len = len(audio)
                target_len = int(orig_len * (48000 / sr))
                indices = np.linspace(0, orig_len - 1, target_len)
                audio = np.interp(indices, np.arange(orig_len), audio)
            
            if len(audio) > 2048:
                audio = audio[:2048]
            elif len(audio) < 2048:
                audio = np.pad(audio, (0, 2048 - len(audio)))
                
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                audio = audio / max_val
            
            return torch.FloatTensor(audio)
        except Exception as e:
            print(f"Error loading file {ir_path}: {e}")
            return torch.zeros(2048)

def train_model():
    print("Starting training...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    if device.type == 'cuda':
        print("Using CUDA device:", torch.cuda.get_device_name(0))
    else:
        print("Using CPU.")

    torch.backends.cudnn.benchmark = True
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True

    # Hyperparameter settings
    batch_size = 32
    num_workers = 2
    epochs = 300  # Extended training
    patience = 10  # Number of epochs where if validation loss doesn't improve, training stops

    # Initialize TensorBoard writer
    writer = SummaryWriter(log_dir="logs")

    # Load dataset and split into training and validation sets (90% / 10%)
    dataset = IRDataset("E:\\IRs")  # Adjust path as needed
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True if device.type == 'cuda' else False,
        persistent_workers=True,
        prefetch_factor=2,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True if device.type == 'cuda' else False
    )
    
    model = CabinetIRGenerator(device=device).to(device)
    
    scaler = GradScaler(enabled=(device.type == 'cuda'))
    # Combined loss: MSE + L1
    mse_loss_fn = nn.MSELoss()
    l1_loss_fn = nn.L1Loss()
    
    optimizer = optim.Adam(model.parameters(), lr=0.0002, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    best_val_loss = float('inf')
    epochs_no_improve = 0

    try:
        for epoch in range(epochs):
            model.train()
            total_train_loss = 0
            batch_count = 0
            
            for batch_idx, real_irs in enumerate(train_loader):
                try:
                    real_irs = real_irs.to(device)
                    current_batch_size = real_irs.size(0)
                    
                    optimizer.zero_grad(set_to_none=True)
                    
                    with autocast(device_type=device.type, enabled=(device.type == 'cuda')):
                        z = torch.randn(current_batch_size, 256, device=device)
                        generated_irs = model(z)
                        loss_mse = mse_loss_fn(generated_irs, real_irs)
                        loss_l1 = l1_loss_fn(generated_irs, real_irs)
                        loss = 0.5 * loss_mse + 0.5 * loss_l1
                    
                    if device.type == 'cuda':
                        scaler.scale(loss).backward()
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()

                    total_train_loss += loss.item()
                    batch_count += 1
                    
                    if batch_idx % 10 == 0:
                        print(f"Epoch {epoch+1}/{epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
                        
                except Exception as e:
                    print(f"Error processing batch {batch_idx}: {e}")
                    continue
            
            avg_train_loss = total_train_loss / batch_count if batch_count > 0 else float('inf')
            writer.add_scalar("Loss/Train", avg_train_loss, epoch)
            
            # Validace po každé epoše
            model.eval()
            total_val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for real_irs in val_loader:
                    real_irs = real_irs.to(device)
                    current_batch_size = real_irs.size(0)
                    with autocast(device_type=device.type, enabled=(device.type == 'cuda')):
                        z = torch.randn(current_batch_size, 256, device=device)
                        generated_irs = model(z)
                        loss_mse = mse_loss_fn(generated_irs, real_irs)
                        loss_l1 = l1_loss_fn(generated_irs, real_irs)
                        loss = 0.5 * loss_mse + 0.5 * loss_l1
                    total_val_loss += loss.item()
                    val_batches += 1
            avg_val_loss = total_val_loss / val_batches if val_batches > 0 else float('inf')
            writer.add_scalar("Loss/Validation", avg_val_loss, epoch)
            print(f"Epoch {epoch+1} completed, Average train loss: {avg_train_loss:.4f}, Validation loss: {avg_val_loss:.4f}")

            # Scheduler step based on validation loss
            scheduler.step(avg_val_loss)

            # Early stopping
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                epochs_no_improve = 0
                try:
                    torch.save({
                        'epoch': epoch,
                        'model_state_dict': model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'loss': best_val_loss,
                    }, f"ir_model_best.pth")
                except Exception as e:
                    print(f"Error saving best model: {e}")
            else:
                epochs_no_improve += 1
                if epochs_no_improve >= patience:
                    print("Validation loss not improving, stopping training early.")
                    break
                
    except KeyboardInterrupt:
        print("Training interrupted by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        try:
            torch.save(model.state_dict(), "ir_model_final.pth")
            print("Model saved")
        except Exception as e:
            print(f"Error saving final model: {e}")
        writer.close()

if __name__ == "__main__":
    train_model()
